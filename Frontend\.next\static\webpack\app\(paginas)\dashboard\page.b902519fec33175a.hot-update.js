"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(paginas)/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/(paginas)/dashboard/page.tsx":
/*!**********************************************!*\
  !*** ./src/app/(paginas)/dashboard/page.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Box_Skeleton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Skeleton,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Skeleton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Skeleton,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Skeleton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Skeleton,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Skeleton/Skeleton.js\");\n/* harmony import */ var _mui_material_GridLegacy__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @mui/material/GridLegacy */ \"(app-pages-browser)/./node_modules/@mui/material/esm/GridLegacy/GridLegacy.js\");\n/* harmony import */ var _components_weather_WeatherWidget__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../components/weather/WeatherWidget */ \"(app-pages-browser)/./src/app/components/weather/WeatherWidget.tsx\");\n/* harmony import */ var _components_farm_FarmSummary__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../components/farm/FarmSummary */ \"(app-pages-browser)/./src/app/components/farm/FarmSummary.tsx\");\n/* harmony import */ var _components_tasks_TaskList__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../components/tasks/TaskList */ \"(app-pages-browser)/./src/app/components/tasks/TaskList.tsx\");\n/* harmony import */ var _components_kpi_KpiComponents__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../components/kpi/KpiComponents */ \"(app-pages-browser)/./src/app/components/kpi/KpiComponents.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst Dashboard = ()=>{\n    _s();\n    // Estados para el formulario de eventos y visualización\n    const [selectedDate, setSelectedDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showEventForm, setShowEventForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showEvents, setShowEvents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [events, setEvents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Activar el estado de loading\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Efecto para simular la carga de datos\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchData = async ()=>{\n            setLoading(true);\n            try {\n                await new Promise((resolve)=>setTimeout(resolve, 1500));\n            } catch (error) {\n                console.error(\"Error fetching data:\", error);\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchData();\n    }, []);\n    // Función para manejar la selección de fecha\n    const handleDateClick = (date)=>{\n        setSelectedDate(date);\n        setShowEventForm(true);\n        setShowEvents(false);\n    };\n    const formatearFecha = (fecha)=>{\n        const fechaFormateada = fecha.toLocaleDateString(\"es-ES\", {\n            weekday: \"long\",\n            day: \"numeric\",\n            month: \"long\",\n            year: \"numeric\"\n        });\n        // Dividir la cadena en palabras\n        const palabras = fechaFormateada.split(\" \");\n        // Capitalizar la primera letra del día y del mes\n        palabras[0] = palabras[0].charAt(0).toUpperCase() + palabras[0].slice(1); // día de la semana\n        palabras[3] = palabras[3].charAt(0).toUpperCase() + palabras[3].slice(1); // mes\n        // Unir las palabras de nuevo\n        return palabras.join(\" \");\n    };\n    // Añadir esta función para determinar la estación actual\n    const obtenerEstacionActual = ()=>{\n        const fecha = new Date();\n        const mes = fecha.getMonth() + 1; // getMonth() devuelve 0-11\n        const dia = fecha.getDate();\n        // Verano: 21 de diciembre - 20 de marzo\n        if (mes === 12 && dia >= 21 || mes <= 2 || mes === 3 && dia <= 20) {\n            return \"Verano\";\n        } else if (mes === 3 && dia >= 21 || mes <= 5 || mes === 6 && dia <= 20) {\n            return \"Oto\\xf1o\";\n        } else if (mes === 6 && dia >= 21 || mes <= 8 || mes === 9 && dia <= 20) {\n            return \"Invierno\";\n        } else {\n            return \"Primavera\";\n        }\n    };\n    // Función para determinar el ciclo agrícola\n    const obtenerCicloAgricola = ()=>{\n        const estacion = obtenerEstacionActual();\n        return estacion === \"Oto\\xf1o\" || estacion === \"Invierno\" ? \"Oto\\xf1o-Invierno\" : \"Primavera-Verano\";\n    };\n    // Función para formatear la fecha actual\n    const formatearFechaActual = ()=>{\n        const fecha = new Date();\n        const fechaFormateada = fecha.toLocaleDateString(\"es-ES\", {\n            weekday: \"long\",\n            day: \"numeric\",\n            month: \"long\"\n        });\n        // Dividir la cadena en palabras y capitalizar\n        const palabras = fechaFormateada.split(\" \");\n        palabras[0] = palabras[0].charAt(0).toUpperCase() + palabras[0].slice(1); // día de la semana\n        palabras[3] = palabras[3].charAt(0).toUpperCase() + palabras[3].slice(1); // mes\n        return palabras.join(\" \");\n    };\n    // Función para generar datos de sparkline simulados\n    const generarSparklineData = function(base) {\n        let variacion = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 5;\n        return Array.from({\n            length: 10\n        }).map((_, i)=>({\n                value: Math.round((base + Math.sin(i / 2) * variacion + i * 0.3) * 10) / 10\n            }));\n    };\n    // Función para generar los datos de KPI\n    const generarDatosKPI = ()=>{\n        return [\n            {\n                id: \"temporada\",\n                title: \"Temporada Actual\",\n                value: obtenerEstacionActual(),\n                caption: \"\".concat(obtenerCicloAgricola(), \" - \").concat(formatearFechaActual()),\n                sparklineData: generarSparklineData(70, 3),\n                loading: loading\n            },\n            {\n                id: \"hectareas\",\n                title: \"Hect\\xe1reas Gestionadas\",\n                value: \"1,250 ha\",\n                delta: 8.5,\n                caption: \"vs mes anterior\",\n                sparklineData: generarSparklineData(100, 8),\n                loading: loading\n            },\n            {\n                id: \"servicios\",\n                title: \"Servicios Activos\",\n                value: \"24\",\n                delta: 12.3,\n                caption: \"servicios en curso\",\n                sparklineData: generarSparklineData(20, 4),\n                loading: loading\n            },\n            {\n                id: \"productividad\",\n                title: \"Productividad\",\n                value: \"94%\",\n                delta: 5.2,\n                caption: \"eficiencia promedio\",\n                sparklineData: generarSparklineData(90, 6),\n                loading: loading\n            },\n            {\n                id: \"cultivos\",\n                title: \"Cultivos Monitoreados\",\n                value: \"8 tipos\",\n                delta: -2.1,\n                caption: \"variedades activas\",\n                sparklineData: generarSparklineData(8, 2),\n                loading: loading\n            },\n            {\n                id: \"clima\",\n                title: \"Condiciones Clim\\xe1ticas\",\n                value: \"Favorable\",\n                caption: \"para actividades agr\\xedcolas\",\n                sparklineData: generarSparklineData(75, 10),\n                loading: loading\n            }\n        ];\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Skeleton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        sx: {\n            padding: \"16px\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Skeleton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                sx: {\n                    mb: 4\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Skeleton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    sx: {\n                        display: \"flex\",\n                        flexDirection: \"column\",\n                        mb: 3\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Skeleton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            variant: \"h6\",\n                            fontWeight: \"bold\",\n                            sx: {\n                                color: \"#2E7D32\",\n                                fontFamily: \"Lexend, sans-serif\",\n                                fontSize: {\n                                    xs: \"1.3rem\",\n                                    sm: \"1.6rem\",\n                                    md: \"1.9rem\"\n                                },\n                                lineHeight: 1.2,\n                                whiteSpace: \"nowrap\",\n                                mb: 1\n                            },\n                            children: \"Bienvenido al Dashboard Agropecuario\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Skeleton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            variant: \"subtitle1\",\n                            sx: {\n                                color: \"#666\",\n                                fontFamily: \"Inter\",\n                                fontSize: {\n                                    xs: \"0.9rem\",\n                                    sm: \"1rem\",\n                                    md: \"1.1rem\"\n                                },\n                                lineHeight: 1.3\n                            },\n                            children: \"Gestiona sus Servicios de forma inteligente y eficiente\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 217,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 196,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 195,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_GridLegacy__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                container: true,\n                spacing: 3,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_GridLegacy__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Skeleton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            sx: {\n                                mb: 2\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Skeleton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    variant: \"h6\",\n                                    sx: {\n                                        color: \"#2E7D32\",\n                                        fontFamily: \"Lexend, sans-serif\",\n                                        fontWeight: 600,\n                                        mb: 2\n                                    },\n                                    children: \"M\\xe9tricas Principales\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_kpi_KpiComponents__WEBPACK_IMPORTED_MODULE_5__.KpiGrid, {\n                                    items: generarDatosKPI(),\n                                    columns: {\n                                        xs: 12,\n                                        sm: 6,\n                                        md: 4,\n                                        lg: 2\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_GridLegacy__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_GridLegacy__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            container: true,\n                            spacing: 3,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_GridLegacy__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    md: 8,\n                                    children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Skeleton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        sx: {\n                                            bgcolor: \"#F1F8E9\",\n                                            p: 2,\n                                            borderRadius: 2,\n                                            border: \"1px solid #C5E1A5\",\n                                            minHeight: \"200px\",\n                                            maxHeight: \"fit-content\",\n                                            display: \"flex\",\n                                            flexDirection: \"column\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Skeleton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                sx: {\n                                                    display: \"flex\",\n                                                    justifyContent: \"space-between\",\n                                                    pb: 2,\n                                                    borderBottom: \"1px solid #e0e0e0\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Skeleton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        variant: \"text\",\n                                                        width: \"40%\",\n                                                        height: 32\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Skeleton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        variant: \"text\",\n                                                        width: \"15%\",\n                                                        height: 24\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Skeleton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                sx: {\n                                                    mt: 2,\n                                                    flex: 1\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_GridLegacy__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    container: true,\n                                                    spacing: 2,\n                                                    children: [\n                                                        1,\n                                                        2,\n                                                        3,\n                                                        4,\n                                                        5,\n                                                        6\n                                                    ].map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_GridLegacy__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            item: true,\n                                                            xs: 12,\n                                                            sm: 6,\n                                                            md: 4,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Skeleton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                sx: {\n                                                                    p: 2,\n                                                                    bgcolor: \"#f8fafc\",\n                                                                    borderRadius: 2,\n                                                                    height: \"100px\"\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Skeleton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        variant: \"text\",\n                                                                        width: \"80%\",\n                                                                        height: 24\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 296,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Skeleton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        variant: \"text\",\n                                                                        width: \"60%\",\n                                                                        height: 20,\n                                                                        sx: {\n                                                                            mt: 1\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 297,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Skeleton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        variant: \"text\",\n                                                                        width: \"70%\",\n                                                                        height: 20,\n                                                                        sx: {\n                                                                            mt: 1\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 303,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 288,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, item, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 287,\n                                                            columnNumber: 25\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 285,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 17\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_farm_FarmSummary__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_GridLegacy__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    md: 4,\n                                    children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Skeleton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        sx: {\n                                            bgcolor: \"#F1F8E9\",\n                                            p: 2,\n                                            borderRadius: 2,\n                                            border: \"1px solid #C5E1A5\",\n                                            height: \"100%\",\n                                            maxHeight: \"400px\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Skeleton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                variant: \"text\",\n                                                width: \"60%\",\n                                                height: 24\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Skeleton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                variant: \"rectangular\",\n                                                height: 200,\n                                                sx: {\n                                                    mt: 2\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Skeleton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                sx: {\n                                                    mt: 2\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Skeleton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        variant: \"text\",\n                                                        width: \"40%\",\n                                                        height: 20\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Skeleton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        variant: \"text\",\n                                                        width: \"60%\",\n                                                        height: 20\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 335,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Skeleton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        variant: \"text\",\n                                                        width: \"80%\",\n                                                        height: 20\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 333,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 17\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_weather_WeatherWidget__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 340,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 254,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_GridLegacy__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_GridLegacy__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            container: true,\n                            spacing: 3,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_GridLegacy__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                md: 8,\n                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Skeleton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    sx: {\n                                        bgcolor: \"#F1F8E9\",\n                                        p: 2,\n                                        borderRadius: 2,\n                                        border: \"1px solid #C5E1A5\",\n                                        height: \"100%\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Skeleton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            sx: {\n                                                display: \"flex\",\n                                                justifyContent: \"space-between\",\n                                                pb: 2,\n                                                borderBottom: \"1px solid #e0e0e0\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Skeleton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    variant: \"text\",\n                                                    width: \"40%\",\n                                                    height: 32\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 369,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Skeleton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    variant: \"text\",\n                                                    width: \"15%\",\n                                                    height: 24\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 370,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 361,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Skeleton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            sx: {\n                                                mt: 2\n                                            },\n                                            children: [\n                                                1,\n                                                2,\n                                                3,\n                                                4,\n                                                5\n                                            ].map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Skeleton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    sx: {\n                                                        mb: 2,\n                                                        p: 2,\n                                                        bgcolor: \"#f8fafc\",\n                                                        borderRadius: 2\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Skeleton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        sx: {\n                                                            display: \"flex\",\n                                                            justifyContent: \"space-between\",\n                                                            alignItems: \"flex-start\"\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Skeleton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                sx: {\n                                                                    display: \"flex\",\n                                                                    gap: 1,\n                                                                    width: \"70%\"\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Skeleton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        variant: \"circular\",\n                                                                        width: 24,\n                                                                        height: 24\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 393,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Skeleton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                        sx: {\n                                                                            flex: 1\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Skeleton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                                variant: \"text\",\n                                                                                width: \"80%\",\n                                                                                height: 24\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 399,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Skeleton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                                variant: \"text\",\n                                                                                width: \"60%\",\n                                                                                height: 20\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 404,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 398,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 392,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Skeleton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                sx: {\n                                                                    display: \"flex\",\n                                                                    flexDirection: \"column\",\n                                                                    alignItems: \"flex-end\",\n                                                                    gap: 1\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Skeleton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        variant: \"rectangular\",\n                                                                        width: 80,\n                                                                        height: 24,\n                                                                        sx: {\n                                                                            borderRadius: 1\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 419,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Skeleton_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        variant: \"text\",\n                                                                        width: 100,\n                                                                        height: 20\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 425,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 411,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 385,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, item, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 23\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 374,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 351,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_tasks_TaskList__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    limit: 5\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 433,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 349,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 348,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 347,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n                lineNumber: 231,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\(paginas)\\\\dashboard\\\\page.tsx\",\n        lineNumber: 193,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Dashboard, \"vfGlBH1uiFcXIKis86ovCe1IjMk=\");\n_c = Dashboard;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Dashboard);\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(paginas)/dashboard/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/components/kpi/KpiComponents.tsx":
/*!**************************************************!*\
  !*** ./src/app/components/kpi/KpiComponents.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   KpiCard: function() { return /* binding */ KpiCard; },\n/* harmony export */   KpiGrid: function() { return /* binding */ KpiGrid; },\n/* harmony export */   \"default\": function() { return /* binding */ KpiComponentsDemo; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! prop-types */ \"(app-pages-browser)/./node_modules/prop-types/index.js\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_17__);\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,Grid,IconButton,Skeleton,Tooltip,Typography,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/styles/useTheme.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,Grid,IconButton,Skeleton,Tooltip,Typography,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,Grid,IconButton,Skeleton,Tooltip,Typography,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/CardContent/CardContent.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,Grid,IconButton,Skeleton,Tooltip,Typography,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,Grid,IconButton,Skeleton,Tooltip,Typography,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,Grid,IconButton,Skeleton,Tooltip,Typography,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Skeleton/Skeleton.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,Grid,IconButton,Skeleton,Tooltip,Typography,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Avatar/Avatar.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,Grid,IconButton,Skeleton,Tooltip,Typography,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Tooltip/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,Grid,IconButton,Skeleton,Tooltip,Typography,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Card,CardContent,Grid,IconButton,Skeleton,Tooltip,Typography,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/esm/Grid/Grid.js\");\n/* harmony import */ var _mui_icons_material_ArrowUpward__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @mui/icons-material/ArrowUpward */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/ArrowUpward.js\");\n/* harmony import */ var _mui_icons_material_ArrowDownward__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @mui/icons-material/ArrowDownward */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/ArrowDownward.js\");\n/* harmony import */ var _mui_icons_material_InfoOutlined__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @mui/icons-material/InfoOutlined */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/InfoOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_Line_LineChart_ResponsiveContainer_recharts__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Line,LineChart,ResponsiveContainer!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Line_LineChart_ResponsiveContainer_recharts__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Line,LineChart,ResponsiveContainer!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/LineChart.js\");\n/* harmony import */ var _barrel_optimize_names_Line_LineChart_ResponsiveContainer_recharts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Line,LineChart,ResponsiveContainer!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/Line.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n/**\r\n * KpiCard\r\n * Props:\r\n *  - title: string\r\n *  - value: string | number\r\n *  - delta: number (percent change, positive or negative)\r\n *  - caption: small text under value (string)\r\n *  - icon: React node (optional)\r\n *  - sparklineData: [{ value: number, label?: string }] (optional)\r\n *  - loading: bool\r\n *  - onClick: function (optional)\r\n *  - sx: additional sx styles\r\n */ function KpiCard(param) {\n    let { title, value, delta, caption, icon, sparklineData, loading, onClick, sx } = param;\n    _s();\n    const theme = (0,_barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    const positive = typeof delta === \"number\" ? delta >= 0 : null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        elevation: 2,\n        sx: {\n            cursor: onClick ? \"pointer\" : \"default\",\n            height: \"100%\",\n            display: \"flex\",\n            flexDirection: \"column\",\n            ...sx\n        },\n        onClick: onClick,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            sx: {\n                display: \"flex\",\n                flexDirection: \"column\",\n                gap: 1,\n                flex: 1\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    alignItems: \"flex-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    variant: \"subtitle2\",\n                                    color: \"text.secondary\",\n                                    noWrap: true,\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, this),\n                                loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    variant: \"text\",\n                                    width: 120,\n                                    height: 36\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    variant: \"h5\",\n                                    sx: {\n                                        mt: 0.5,\n                                        fontWeight: 600\n                                    },\n                                    children: value\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 15\n                                }, this),\n                                caption && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    variant: \"caption\",\n                                    color: \"text.secondary\",\n                                    display: \"block\",\n                                    children: caption\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            gap: 1,\n                            children: [\n                                icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    variant: \"rounded\",\n                                    sx: {\n                                        width: 40,\n                                        height: 40,\n                                        bgcolor: theme.palette.action.hover\n                                    },\n                                    children: icon\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    title: \"M\\xe1s info\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        size: \"small\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_InfoOutlined__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            fontSize: \"small\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: \"space-between\",\n                    mt: 1,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            gap: 0.5,\n                            children: typeof delta === \"number\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: 0.5,\n                                children: [\n                                    positive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_ArrowUpward__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        sx: {\n                                            fontSize: 18,\n                                            color: \"success.main\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_ArrowDownward__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        sx: {\n                                            fontSize: 18,\n                                            color: \"error.main\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        variant: \"caption\",\n                                        sx: {\n                                            color: positive ? \"success.main\" : \"error.main\",\n                                            fontWeight: 600\n                                        },\n                                        children: [\n                                            Math.abs(delta).toFixed(1),\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        variant: \"caption\",\n                                        color: \"text.secondary\",\n                                        children: \"vs prev\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                variant: \"caption\",\n                                color: \"text.secondary\",\n                                children: \"\\xa0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            sx: {\n                                width: 120,\n                                height: 40\n                            },\n                            children: sparklineData && sparklineData.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Line_LineChart_ResponsiveContainer_recharts__WEBPACK_IMPORTED_MODULE_14__.ResponsiveContainer, {\n                                width: \"100%\",\n                                height: \"100%\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Line_LineChart_ResponsiveContainer_recharts__WEBPACK_IMPORTED_MODULE_15__.LineChart, {\n                                    data: sparklineData,\n                                    margin: {\n                                        top: 0,\n                                        right: 0,\n                                        left: 0,\n                                        bottom: 0\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Line_LineChart_ResponsiveContainer_recharts__WEBPACK_IMPORTED_MODULE_16__.Line, {\n                                        type: \"monotone\",\n                                        dataKey: \"value\",\n                                        stroke: theme.palette.primary.main,\n                                        strokeWidth: 2,\n                                        dot: false\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                    lineNumber: 131,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n            lineNumber: 79,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n        lineNumber: 68,\n        columnNumber: 5\n    }, this);\n}\n_s(KpiCard, \"VrMvFCCB9Haniz3VCRPNUiCauHs=\", false, function() {\n    return [\n        _barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n    ];\n});\n_c = KpiCard;\nKpiCard.propTypes = {\n    title: (prop_types__WEBPACK_IMPORTED_MODULE_17___default().string).isRequired,\n    value: prop_types__WEBPACK_IMPORTED_MODULE_17___default().oneOfType([\n        (prop_types__WEBPACK_IMPORTED_MODULE_17___default().string),\n        (prop_types__WEBPACK_IMPORTED_MODULE_17___default().number)\n    ]),\n    delta: (prop_types__WEBPACK_IMPORTED_MODULE_17___default().number),\n    caption: (prop_types__WEBPACK_IMPORTED_MODULE_17___default().string),\n    icon: (prop_types__WEBPACK_IMPORTED_MODULE_17___default().node),\n    sparklineData: (prop_types__WEBPACK_IMPORTED_MODULE_17___default().array),\n    loading: (prop_types__WEBPACK_IMPORTED_MODULE_17___default().bool),\n    onClick: (prop_types__WEBPACK_IMPORTED_MODULE_17___default().func),\n    sx: (prop_types__WEBPACK_IMPORTED_MODULE_17___default().object)\n};\n/**\r\n * KpiGrid: simple responsive grid to layout KPI cards\r\n * Props:\r\n *  - items: array of { id, title, value, delta, caption, icon, sparklineData, loading }\r\n *  - columns: responsive columns object (optional)\r\n */ function KpiGrid(param) {\n    let { items = [], columns = {\n        xs: 12,\n        sm: 6,\n        md: 4\n    } } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n        container: true,\n        spacing: 2,\n        children: items.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                item: true,\n                xs: columns.xs,\n                sm: columns.sm,\n                md: columns.md,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(KpiCard, {\n                    ...item\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                    lineNumber: 242,\n                    columnNumber: 11\n                }, this)\n            }, item.id, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                lineNumber: 235,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n        lineNumber: 233,\n        columnNumber: 5\n    }, this);\n}\n_c1 = KpiGrid;\nKpiGrid.propTypes = {\n    items: (prop_types__WEBPACK_IMPORTED_MODULE_17___default().array),\n    columns: (prop_types__WEBPACK_IMPORTED_MODULE_17___default().object)\n};\n/**\r\n * Demo / Example usage default export\r\n * - Provides example KPIs and shows how to import/use KpiGrid\r\n */ function KpiComponentsDemo() {\n    const mockSpark = function() {\n        let start = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 10;\n        return Array.from({\n            length: 10\n        }).map((_, i)=>({\n                value: Math.round((start + Math.sin(i / 2) * 3 + i * 0.5) * 10) / 10\n            }));\n    };\n    const demoItems = [\n        {\n            id: \"ingresos\",\n            title: \"Ingresos (mes)\",\n            value: \"$ 1.250.000\",\n            delta: 8.2,\n            caption: \"vs mes anterior\",\n            sparklineData: mockSpark(100)\n        },\n        {\n            id: \"hectareas\",\n            title: \"Hect\\xe1reas atendidas\",\n            value: \"1.230 ha\",\n            delta: -2.4,\n            caption: \"\\xfaltimos 30 d\\xedas\",\n            sparklineData: mockSpark(50)\n        },\n        {\n            id: \"utilizacion\",\n            title: \"Utilizaci\\xf3n maquinaria\",\n            value: \"72 %\",\n            delta: 4.6,\n            caption: \"promedio flota\",\n            sparklineData: mockSpark(70)\n        },\n        {\n            id: \"costohora\",\n            title: \"Costo por hora\",\n            value: \"$ 3.800\",\n            delta: 1.1,\n            caption: \"comb, mano de obra\",\n            sparklineData: mockSpark(30)\n        },\n        {\n            id: \"ontime\",\n            title: \"Trabajos a tiempo\",\n            value: \"91 %\",\n            delta: 0.8,\n            caption: \"a tiempo\",\n            sparklineData: mockSpark(90)\n        },\n        {\n            id: \"mantenimiento\",\n            title: \"Mantenimientos pr\\xf3ximos\",\n            value: \"3 m\\xe1quinas\",\n            caption: \"en los pr\\xf3ximos 7 d\\xedas\",\n            sparklineData: mockSpark(20)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        p: 2,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                variant: \"h6\",\n                sx: {\n                    mb: 2\n                },\n                children: \"KPIs reutilizables — demo\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                lineNumber: 316,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(KpiGrid, {\n                items: demoItems\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                lineNumber: 319,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                mt: 3,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        variant: \"body2\",\n                        color: \"text.secondary\",\n                        children: \"Consejos:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                        lineNumber: 322,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    variant: \"body2\",\n                                    children: [\n                                        \"Pasa datos reales desde tu API y actualiza los props:\",\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                            children: \"value\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 15\n                                        }, this),\n                                        \", \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                            children: \"delta\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 35\n                                        }, this),\n                                        \" y\",\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                            children: \"sparklineData\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 15\n                                        }, this),\n                                        \".\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    variant: \"body2\",\n                                    children: \"Para sparklines puedes usar datos de los \\xfaltimos 7/14/30 puntos (d\\xeda/semana) seg\\xfan tu granularidad.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                    lineNumber: 334,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                lineNumber: 333,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Card_CardContent_Grid_IconButton_Skeleton_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    variant: \"body2\",\n                                    children: \"Si usas TypeScript simplemente tipa las props y exporta los componentes.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                    lineNumber: 340,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                        lineNumber: 325,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n                lineNumber: 321,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\SpringBoot\\\\Servicios\\\\Frontend\\\\src\\\\app\\\\components\\\\kpi\\\\KpiComponents.tsx\",\n        lineNumber: 315,\n        columnNumber: 5\n    }, this);\n}\n_c2 = KpiComponentsDemo;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"KpiCard\");\n$RefreshReg$(_c1, \"KpiGrid\");\n$RefreshReg$(_c2, \"KpiComponentsDemo\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/components/kpi/KpiComponents.tsx\n"));

/***/ })

});